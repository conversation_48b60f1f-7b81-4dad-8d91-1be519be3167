<script setup lang="ts">
import { Counter } from '../../ui/counter/counter';
import Button from '~/components/Button.vue';

const counter = new Counter();
</script>

<template>
    <div>
        <div class="text-4xl">Counter: {{ counter.count }}</div>
        <button @click="counter.increment()">Increment</button>
        <button @click="counter.decrement()">Decrement</button>

        <Button @click="counter.increment()"> Button </Button>
    </div>
</template>
