<template>
    <button 
        :class="[
            'px-3 py-1 rounded-full text-sm font-medium transition-colors cursor-pointer outline-none focus:outline-none',
            active 
                ? 'bg-blue-500 text-white' 
                : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
        ]"
        @click="$emit('click')"
    >
        <slot />
    </button>
</template>

<script setup lang="ts">
interface Props {
    active?: boolean;
}

defineProps<Props>();
defineEmits<{
    click: [];
}>();
</script>
