<script setup lang="ts">
import Input from '~/components/Input.vue';
import Badge from '~/components/Badge.vue';
import { useClass } from '../../ui/adapters/vue';
import { TodoList } from '../../ui/todo-list/todo-list';

const todoList = useClass(new TodoList());
</script>

<template>
    <div>
        <div class="flex items-center gap-2">
            <Input v-model="todoList.input" placeholder="Add todo" />
            <Button @click="todoList.add">Add</Button>
        </div>

        <div class="divide-y bg-white border border-gray-200 divide-gray-200 rounded-lg mt-4">
            <div v-for="item of todoList.items" :key="item.id.value" class="flex items-center justify-between px-3 min-h-12">
                <div class="flex items-center gap-2">
                    <input v-model="item.completed" type="checkbox" @input="todoList.toggle(item)" />
                    <span>{{ item.text }}</span>
                </div>

                <Button class="h-6" @click="todoList.remove(item)">Remove</Button>
            </div>
        </div>
    </div>
</template>
