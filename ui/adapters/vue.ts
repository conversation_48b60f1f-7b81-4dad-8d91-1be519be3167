import { reactive, onMounted, onUnmounted } from 'vue';
import { ChangeNotifier } from '../change-notifier';

export function useClass<T extends ChangeNotifier>(notifier: T): T {
    const state = reactive({ version: 0 });
    const update = () => state.version++;
    onMounted(() => notifier.addListener(update));
    onUnmounted(() => notifier.removeListener(update));
    return new Proxy(notifier, {
        get(target, prop) {
            state.version; // binds vue to the notifier in this line.
            const value = (target as any)[prop];
            console.log('proxy value', { value, prop, target });
            if (typeof value === 'function') {
                return value.bind(target);
            }
            return value;
        },
        set(target, prop, value) {
            console.log('proxy set', { prop, value, target });
            update();
            return true;
        },
    });
}
