import { ChangeNotifier } from '../change-notifier';
import { Todo } from './todo';

export enum TodoListFilter {
    All,
    Incomplete,
    Completed,
}

export class TodoList extends ChangeNotifier {
    #items: Todo[] = [];
    public input = '';
    public filter = TodoListFilter.All;

    public get items() {
        switch (this.filter) {
            case TodoListFilter.All:
                return this.#items;
            case TodoListFilter.Incomplete:
                return this.#items.filter(item => !item.completed);
            case TodoListFilter.Completed:
                return this.#items.filter(item => item.completed);
            default:
                return this.#items;
        }
    }

    public add() {
        this.#items.push(Todo.create(this.input));
        this.input = '';
        this.notifyListeners();
    }

    public toggle(todo: Todo) {
        todo.toggle();
        this.notifyListeners();
    }

    public remove(item: Todo) {
        this.#items = this.#items.filter(i => !i.equals(item));
        this.notifyListeners();
    }

    public showAll() {
        this.filter = TodoListFilter.All;
        this.notifyListeners();
    }

    public showIncomplete() {
        this.filter = TodoListFilter.Incomplete;
        this.notifyListeners();
    }

    public showCompleted() {
        this.filter = TodoListFilter.Completed;
        this.notifyListeners();
    }

    public get showingCompleted() {
        return this.filter === TodoListFilter.Completed;
    }

    public get showingIncomplete() {
        return this.filter === TodoListFilter.Incomplete;
    }

    public get showingAll() {
        return this.filter === TodoListFilter.All;
    }
}
