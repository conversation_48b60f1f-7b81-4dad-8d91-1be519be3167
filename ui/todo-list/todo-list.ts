import { ChangeNotifier } from '../change-notifier';
import { Todo } from './todo';

export class TodoList extends ChangeNotifier {
    #items: Todo[] = [];
    public input = '';

    public get items() {
        return this.#items;
    }

    public add() {
        this.#items.push(Todo.create(this.input));
        this.input = '';
        this.notifyListeners();
    }

    public toggle(todo: Todo) {
        todo.toggle();
        this.notifyListeners();
    }

    public remove(item: Todo) {
        this.#items = this.#items.filter(i => !i.equals(item));
        this.notifyListeners();
    }
}
