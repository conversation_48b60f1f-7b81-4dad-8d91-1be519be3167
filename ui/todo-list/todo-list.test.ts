import { describe, expect, it, vi } from 'vitest';
import { TodoList, TodoListFilter } from './todo-list';

describe('TodoList', () => {
    describe('add', () => {
        it('should add a todo', () => {
            const todoList = new TodoList();
            todoList.input = 'Item 1';
            todoList.add();
            expect(todoList.items).toHaveLength(1);
            expect(todoList.items[0].text).toEqual('Item 1');
            expect(todoList.input).toBe('');
        });
    });

    describe('remove', () => {
        it('should remove the item from the list', () => {
            const todoList = new TodoList();
            todoList.input = 'Item 1';
            todoList.add();
            todoList.remove(todoList.items[0]);
            expect(todoList.items).toHaveLength(0);
        });
    });
});
