@import 'tailwindcss';
@plugin '@tailwindcss/forms';
@plugin '@tailwindcss/typography';

:root {
    --color-danger: var(--color-red-500);
    --color-danger-contrast: var(--color-white);
    --color-danger-50: var(--color-red-50);
    --color-danger-100: var(--color-red-100);
    --color-danger-200: var(--color-red-200);
    --color-danger-300: var(--color-red-300);
    --color-danger-400: var(--color-red-400);
    --color-danger-500: var(--color-red-500);
    --color-danger-600: var(--color-red-600);
    --color-danger-700: var(--color-red-700);
    --color-danger-800: var(--color-red-800);
    --color-danger-900: var(--color-red-900);
    --color-danger-950: var(--color-red-950);

    --color-primary: var(--color-blue-500);
    --color-primary-contrast: var(--color-white);
    --color-primary-50: var(--color-blue-50);
    --color-primary-100: var(--color-blue-100);
    --color-primary-200: var(--color-blue-200);
    --color-primary-300: var(--color-blue-300);
    --color-primary-400: var(--color-blue-400);
    --color-primary-500: var(--color-blue-500);
    --color-primary-600: var(--color-blue-600);
    --color-primary-700: var(--color-blue-700);
    --color-primary-800: var(--color-blue-800);
    --color-primary-900: var(--color-blue-900);
    --color-primary-950: var(--color-blue-950);
}