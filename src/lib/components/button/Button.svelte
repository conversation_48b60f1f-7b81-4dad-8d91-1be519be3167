<script lang="ts">
    import type { Snippet } from 'svelte';

    interface Props {
        /** Button contents */
        children: Snippet;

        /** The onclick event handler */
        onclick?: () => void;

        /** The CSS classes */
        class?: string;

        /** The color name from the color palette */
        color?: string;

        /**
         * The button variant to use.
         */
        variant?: 'filled' | 'outlined' | 'ghost';
    }

    const { children, color, variant = 'filled', onclick, ...props }: Props = $props();
</script>

<button
    type="button"
    {onclick}
    class={['f-button', `f-button-${variant}`, props.class]}
    style:--btn-color={color ? `var(--color-${color})` : undefined}
    style:--btn-text={color ? `var(--color-on-${color})` : undefined}
    data-color={color ?? 'default'}
>
    {@render children?.()}
</button>
