<script module lang="ts">
    import { defineMeta } from '@storybook/addon-svelte-csf';
    import Button from '$lib/components/button/Button.svelte';
    import { fn } from 'storybook/test';

    // More on how to set up stories at: https://storybook.js.org/docs/writing-stories
    const { Story } = defineMeta({
        title: 'Components/Button',
        component: Button,
        tags: ['autodocs'],
        argTypes: {
            color: {
                control: { type: 'select' },
                options: ['primary', 'danger'],
            },
        },
        args: {
            onclick: fn(),
        },
    });
</script>

<!-- More on writing stories with args: https://storybook.js.org/docs/writing-stories/args -->
<Story name="Everything" asChild>
    <div class="flex flex-col gap-3 border border-(--color-surface-muted) bg-(--color-background) p-4" data-theme="light">
        <div>
            <Button>Default</Button>
            <Button color="primary">Primary</Button>
            <Button color="error">Error</Button>
            <Button color="success">Success</Button>
            <Button color="example" class="border-4 border-blue-500">Custom Color Example</Button>
        </div>
        <div>
            <Button>Default (filled)</Button>
            <Button variant="filled">Filled</Button>
            <Button variant="outlined">Outline</Button>
            <Button variant="ghost">Ghost</Button>
        </div>
        <div>
            <Button color="primary">Primary (filled)</Button>
            <Button color="primary" variant="filled">Filled</Button>
            <Button color="primary" variant="outlined">Outline</Button>
            <Button color="primary" variant="ghost">Ghost</Button>
        </div>
        <div>
            <Button color="secondary">Primary (filled)</Button>
            <Button color="secondary" variant="filled">Filled</Button>
            <Button color="secondary" variant="outlined">Outline</Button>
            <Button color="secondary" variant="ghost">Ghost</Button>
        </div>
    </div>
</Story>

<Story name="With Tailwind" asChild>
    <Button class="text-4xl font-thin">Testing</Button>
</Story>

<Story name="Colors" args={{ color: 'error' }} asChild>
    <Button color="error">Danger</Button>
    <Button color="error-300">Danger</Button>
    <Button color="error-600">Danger</Button>
</Story>
<Story name="Custom Color" asChild>
    <Button color="error">Danger</Button>
    <Button color="error-100">Danger</Button>
    <Button color="error-600">Danger</Button>
</Story>
