@layer components {
    .f-button {
        /* Button variables */
        --btn-color-bg: var(--default-color-bg);
        --btn-color-on-bg: var(--default-color-on-bg);
        --btn-color-border: var(--default-color-border);

        /* Button States */
        --btn-color-bg-hover: var(--default-color-bg-hover);
        --btn-color-bg-active: var(--default-color-bg-active);

        /* Styles */
        border-width: var(--border);
        @apply border text-(--btn-color-on-bg) focus:outline rounded-(--radius-field) shadow-xs active:shadow-none px-4 py-2 active:translate-y-[0.5px] cursor-pointer;

        /* Transitions */
        @apply transition-shadow transition-colors duration-200;

        /* Focus Styles */
        @apply focus:outline-none focus:ring-2 focus:ring-(--btn-color-border) focus:ring-offset-2 transition-[ring];

        background-color: var(--btn-color-bg);
        border-color: var(--btn-color-border);

        /* Fix for the default color only */
        &[data-color="default"] {
            &:focus {
                --tw-ring-color: --alpha(var(--btn-color-on-bg) / 50%);
            }
        }

        &.f-button-filled {
            /*@apply hover:brightness-95 active:brightness-90;*/
            &:hover {
                background-color: --alpha(var(--btn-color-bg) / 90%);
            }
        }


        /* Variants */
        &.f-button-outlined {
            @apply border-(--btn-border-color) text-(--btn-color-bg);
            border-color: --alpha(var(--btn-color-bg) / 30%);
            background-color: --alpha(var(--btn-color-bg) / 10%);
            &:hover {
                background-color: --alpha(var(--btn-color-bg) / 20%);
            }

            /* This is a fix for the default color only */
            &[data-color="default"] {
                @apply text-(--default-color-on-bg);
                border-color: --alpha(var(--default-color-on-bg) / 30%);
            }
        }

        &.f-button-ghost {
            @apply border-transparent bg-transparent shadow-none text-(--btn-color-bg);
            /*border-color: --alpha(var(--btn-color-bg) / 30%);*/
            /*background-color: --alpha(var(--btn-color-bg) / 10%);*/
            &:hover {
                background-color: --alpha(var(--btn-color-bg) / 20%);
            }

            /* This is a fix for the default color only */
            &[data-color="default"] {
                @apply text-(--default-color-on-bg);
                border-color: --alpha(var(--default-color-on-bg) / 30%);
            }
        }
    }
}