{"name": "era-projects", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "test:coverage": "vitest run --coverage"}, "keywords": [], "author": "", "license": "ISC", "packageManager": "pnpm@10.11.0", "devDependencies": {"@vitest/coverage-istanbul": "^3.1.4", "prettier": "3.5.3", "vitest": "^3.1.4"}, "dependencies": {"typescript": "^5.8.3", "vue": "^3.5.16"}}